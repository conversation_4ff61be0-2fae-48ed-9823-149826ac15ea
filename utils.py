from bs4 import BeautifulSoup
import time

def extract_alleen_tekst_uit_html(html: str) -> str:
    """
    Haalt alleen de zichtbare tekst uit een HTML-string.
    """
    soup = BeautifulSoup(html, "html.parser")
    return soup.get_text(separator="\n", strip=True)


import requests
import os
import re
import random

def wacht_random_groq(min_sec=10.0, max_sec=15.0):
    """Wacht een willekeurige tijd tussen min_sec en max_sec seconden"""
    aantal_seconden = random.uniform(min_sec, max_sec)
    print(f"⏳ Wachten voor {round(aantal_seconden, 2)} seconden...")
    time.sleep(aantal_seconden)
    print("🚀 Verder gaan!")

def lees_prompt(bestandsnaam: str) -> str:
    """
    Leest de prompt uit een tekstbestand in de prompts-map.
    """
    pad = os.path.join(os.path.dirname(__file__), "prompts", bestandsnaam)
    with open(pad, "r", encoding="utf-8") as f:
        return f.read()
    
def haal_html_op(url):
    response = requests.get(url)
    response.raise_for_status()
    return response.text

def stuur_prompt_naar_groq(prompt, html):
    print("Stuur prompt naar groq, we wachten eerst een paar seconden")
    wacht_random_groq()
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("❌ GROQ_API_KEY niet gevonden in .env bestand.")
    endpoint = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "deepseek-r1-distill-llama-70b",
        "messages": [
            {"role": "user", "content": f"{prompt}\n\n{html}"}
        ],
        "max_tokens": 1400,
        "temperature": 0.2
    }
    response = requests.post(endpoint, headers=headers, json=data)
    response.raise_for_status()
    return response.json()["choices"][0]["message"]["content"]

def strip_think_blocks(text: str) -> str:
    """
    Verwijdert alle <think>...</think> blokken uit de tekst.
    Werkt met DeepSeek R1 model dat thinking blocks gebruikt.
    """
    if not text:
        return ""

    # Verwijder <think>...</think> blokken (case insensitive)
    cleaned = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL | re.IGNORECASE)

    # Verwijder ook eventuele andere thinking patterns
    cleaned = re.sub(r"<thinking>.*?</thinking>", "", cleaned, flags=re.DOTALL | re.IGNORECASE)

    # Verwijder extra whitespace en newlines
    cleaned = re.sub(r'\n\s*\n', '\n', cleaned)  # Meerdere newlines naar enkele
    cleaned = cleaned.strip()

    return cleaned

def genereer_werkzoeken_url(vac_id: int) -> str:
    """
    Haalt de vacature op via vacID, stuurt prompt en html naar Groq, en retourneert de werkzoeken.nl zoek-URL.
    """
    vacature_url = f"https://www.talentenscout.com/vacature.php?vacID={vac_id}"
    html = haal_html_op(vacature_url)

    prompt = lees_prompt("werkzoeken_url_genereren.txt")

  

    response = stuur_prompt_naar_groq(prompt, html)
    zoek_url = strip_think_blocks(response).strip()

    return zoek_url

def genereer_Kandidaat_profiel(prompt: str, tekstProfiel: str) -> str:
  

    response = stuur_prompt_naar_groq(prompt, tekstProfiel)
    zoek_url = strip_think_blocks(response).strip()

    return zoek_url

def vacature_from_url(vac_id: int) -> str:
    """
    Haalt de vacature op via vacID, stuurt prompt en html naar Groq, en retourneert de werkzoeken.nl zoek-URL.
    """
    try:
        print(f"🔍 Ophalen vacature van ID: {vac_id}")
        vacature_url = f"https://www.talentenscout.com/vacature.php?vacID={vac_id}"
        html = haal_html_op(vacature_url)
        print(f"✅ HTML opgehaald van: {vacature_url}")

        prompt = lees_prompt("werkzoeken_vacature_from_url.txt")
        print(f"✅ Prompt geladen: werkzoeken_vacature_from_url.txt")

        print("🤖 Versturen naar Groq API...")
        response = stuur_prompt_naar_groq(prompt, html)

        # Strip think blocks voor de finale output
        vacature_data = strip_think_blocks(response).strip()


        if not vacature_data:
            raise ValueError("❌ Geen data ontvangen na het strippen van think blocks")

        print("✅ Vacature data succesvol verwerkt")
        return vacature_data

    except Exception as e:
        print(f"❌ Fout in vacature_from_url: {e}")
        raise

def valideer_werkzoeken_url(url: str) -> bool:
    """
    Valideert of de gegenereerde werkzoeken.nl URL compleet en bruikbaar is.
    Controleert op base-url en enkele verplichte parameters.
    """
    base = "https://www.werkzoeken.nl/cv-database/"
    verplicht = ["filtered=1", "what=", "where=", "r=", "date="]
    if not url.startswith(base):
        return False
    for param in verplicht:
        if param not in url:
            return False
    return True

def test_vacature_from_url(vac_id: int = 115):
    """
    Test functie voor debugging van vacature_from_url
    """
    print(f"🧪 Testing vacature_from_url met vac_id: {vac_id}")
    try:
        result = vacature_from_url(vac_id)
        print(f"🎯 Finale resultaat:")
        print(f"Type: {type(result)}")
        print(f"Lengte: {len(result) if result else 0}")
        print(f"Inhoud: {result}")
        return result
    except Exception as e:
        print(f"💥 Test gefaald: {e}")
        import traceback
        traceback.print_exc()
        return None

# Voorbeeld gebruik:
# url = genereer_werkzoeken_url(115)
# print("Gegenereerde URL:", url)

# Test de vacature_from_url functie:
# test_vacature_from_url(115)

# Gebruik na het genereren:
# zoek_url = genereer_werkzoeken_url(115)
# if not valideer_werkzoeken_url(zoek_url):
#     raise ValueError("❌ De gegenereerde zoek_url is niet compleet of ongeldig.")