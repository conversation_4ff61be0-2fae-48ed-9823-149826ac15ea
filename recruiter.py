from playwright.sync_api import sync_playwright, Timeout<PERSON>rror as PlaywrightTimeoutError
from url import genereer_werkzoeken_url, lees_prompt, stuur_prompt_naar_groq
from bs4 import BeautifulSoup
import os
import time
import random
import sys
from random import randint
from pathlib import Path
from dotenv import load_dotenv

# Laad environment variabelen
dotenv_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=dotenv_path)

email = os.getenv("WERKZOEKEN_EMAIL")
wachtwoord = os.getenv("WERKZOEKEN_WACHTWOORD")

if not email or not wachtwoord:
    raise ValueError("❌ Inloggegevens niet gevonden in .env bestand.")

def wacht_random(min_sec=2.0, max_sec=5.0):
    """Wacht een willekeurige tijd tussen min_sec en max_sec seconden"""
    time.sleep(random.uniform(min_sec, max_sec))

def menselijk_typen(page, selector, tekst):
    """Type tekst op een menselijke manier met willekeurige delays"""
    for letter in tekst:
        page.type(selector, letter, delay=randint(50, 200))
    wacht_random(0.2, 0.7)

def setup_stealth_page(page):
    """Setup stealth scripts voor een bestaande page"""
    # Geavanceerde stealth script - volledig ondetecteerbaar
    page.add_init_script("""
    // 1. WebDriver volledig verwijderen - meerdere methoden
    try {
      // Verwijder uit navigator prototype
      const navigatorProto = Object.getPrototypeOf(navigator);
      if (navigatorProto && 'webdriver' in navigatorProto) {
        delete navigatorProto.webdriver;
      }

      // Verwijder uit navigator zelf
      if ('webdriver' in navigator) {
        delete navigator.webdriver;
      }

      // Verwijder uit window
      if ('webdriver' in window) {
        delete window.webdriver;
      }

      // Zorg ervoor dat de property niet bestaat
      if (Object.hasOwnProperty.call(navigator, 'webdriver')) {
        delete navigator.webdriver;
      }
    } catch(e) {}

    // 2. Chrome object
    try {
      window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {}
      };
    } catch(e) {}

    // 3. Plugins - werkende implementatie
    try {
      // Maak fake plugins
      const fakePlugins = [
        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' },
        { name: 'WebKit built-in PDF', filename: 'webkit-pdf-plugin', description: 'Portable Document Format' },
        { name: 'Microsoft Edge PDF Viewer', filename: 'edge-pdf-viewer', description: 'Portable Document Format' }
      ];

      // Zorg dat elk plugin object correct toString heeft
      fakePlugins.forEach(plugin => {
        plugin.toString = () => '[object Plugin]';
      });

      // Maak het een echte PluginArray
      fakePlugins.__proto__ = PluginArray.prototype;
      fakePlugins.namedItem = function(name) {
        return this.find(plugin => plugin.name === name) || null;
      };
      fakePlugins.refresh = function() {};

      Object.defineProperty(navigator, 'plugins', {
        get: () => fakePlugins,
        configurable: true
      });
    } catch(e) {}

    // 4. Languages
    try {
      Object.defineProperty(navigator, 'languages', {
        get: () => ['nl-NL', 'nl', 'en-US', 'en']
      });
    } catch(e) {}

    // 5. Permissions
    try {
      const originalQuery = navigator.permissions.query;
      navigator.permissions.query = function(parameters) {
        if (parameters.name === 'notifications') {
          return Promise.resolve({ state: 'denied' });
        }
        return originalQuery.call(this, parameters);
      };

      // Ook Notification.permission instellen
      Object.defineProperty(Notification, 'permission', {
        get: () => 'denied',
        configurable: true
      });
    } catch(e) {}

    // 6. WebGL spoofing
    try {
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) return 'Google Inc. (NVIDIA Corporation)';
        if (parameter === 37446) return 'ANGLE (NVIDIA Corporation, NVIDIA GeForce GTX 1650 Ti with Max-Q Design/PCIe/SSE2, OpenGL 4.5.0)';
        return getParameter.call(this, parameter);
      };
    } catch(e) {}

    // 7. Video codec spoofing
    try {
      const originalCanPlayType = HTMLVideoElement.prototype.canPlayType;
      HTMLVideoElement.prototype.canPlayType = function(type) {
        if (type.includes('h264') || type.includes('mp4') || type.includes('avc1')) {
          return 'probably';
        }
        if (type.includes('webm') || type.includes('vp8') || type.includes('vp9')) {
          return 'probably';
        }
        if (type.includes('ogg') || type.includes('theora')) {
          return 'maybe';
        }
        return originalCanPlayType.call(this, type);
      };
    } catch(e) {}

    // 8. Audio codec spoofing
    try {
      const originalCanPlayTypeAudio = HTMLAudioElement.prototype.canPlayType;
      HTMLAudioElement.prototype.canPlayType = function(type) {
        if (type.includes('mp3') || type.includes('mpeg')) {
          return 'probably';
        }
        if (type.includes('wav') || type.includes('wave')) {
          return 'probably';
        }
        if (type.includes('ogg') || type.includes('vorbis')) {
          return 'probably';
        }
        if (type.includes('m4a') || type.includes('aac')) {
          return 'probably';
        }
        return originalCanPlayTypeAudio.call(this, type);
      };
    } catch(e) {}
    """)

def login_werkzoeken(page):
    """Log in op werkzoeken.nl"""
    print("🔐 Inloggen op werkzoeken.nl...")
    
    page.goto("https://www.werkzoeken.nl/inloggen")
    
    # Accepteer cookies
    page.wait_for_selector("text=Accepteren")
    wacht_random()
    page.click("text=Accepteren")
    wacht_random()
    
    # Muis bewegen naar het veld
    page.mouse.move(randint(100, 300), randint(200, 250))
    wacht_random()
    
    # Inloggen met menselijk typen
    menselijk_typen(page, "#username", email)
    menselijk_typen(page, "#signin_modal_password", wachtwoord)
    wacht_random()
    
    page.mouse.move(randint(200, 400), randint(300, 350))
    page.click("id=login_modal_submit")
    wacht_random()
    
    # Controleer op foutmelding 'Te veel gebruikers ingelogd op dit account.'
    try:
        page.wait_for_selector("text=Te veel gebruikers ingelogd op dit account.", timeout=3000)
        print("❌ Te veel gebruikers ingelogd op dit account. Script wordt gestopt.")
        wacht_random()
        page.goto("https://www.werkzoeken.nl/uitloggen")
        wacht_random()
        return False
    except PlaywrightTimeoutError:
        pass  # Geen foutmelding gevonden, ga verder
    
    print("✅ Succesvol ingelogd!")
    return True

def zoek_kandidaten(page, context, zoek_id=115):
    """Zoek kandidaten op werkzoeken.nl"""
    zoek_url = genereer_werkzoeken_url(zoek_id)
    print(f"🔍 Zoeken naar kandidaten met URL: {zoek_url}")
    
    page.goto(zoek_url)
    page.wait_for_selector(".vacancies-wrapper")
    
    # Selecteer alle CV-links
    cvs = page.query_selector_all("a.vacancy.vac.cv")
    print(f"🔎 Gevonden {len(cvs)} CV's:\n")
    
    for cv in cvs:
        naam = cv.query_selector("h3").inner_text().strip() if cv.query_selector("h3") else "Onbekend"
        plaats = cv.query_selector("strong").inner_text().strip() if cv.query_selector("strong") else "Onbekend"
        functie = cv.query_selector(".tag").inner_text().strip() if cv.query_selector(".tag") else "Onbekend"
        data_url = cv.get_attribute("data-url") or "Geen URL gevonden"
        
        print(f"👤 {naam} – 📍 {plaats} – 🏷️ {functie}")
        print(f"🔗 {data_url}")
        
        if data_url != "Geen URL gevonden":
            detail_page = context.new_page()
            # Setup stealth voor detail page
            setup_stealth_page(detail_page)

            try:
                detail_page.goto(data_url)
                detail_page.wait_for_selector("div.vacancy-detail-wrapper", timeout=5000)
                html = detail_page.query_selector("div.vacancy-detail-wrapper").inner_html()
                # Alleen de tekst extraheren
                soup = BeautifulSoup(html, "html.parser")
                alleen_tekst = soup.get_text(separator="\n", strip=True)
                prompt = lees_prompt("werkzoeken_html_profiel_tekst.txt")
                groq_output = stuur_prompt_naar_groq(prompt, alleen_tekst)
                print("💡 Groq analyse:")
                print(groq_output)
            except PlaywrightTimeoutError:
                print("❌ Kon vacancy-detail-wrapper niet vinden.")
            except Exception as e:
                print(f"❌ Fout bij verwerken CV: {e}")
            finally:
                detail_page.close()
            print("-" * 40)

def main():
    """Hoofdfunctie die alles combineert"""
    print("🚀 Starten van de ondetecteerbare recruiter bot...")

    with sync_playwright() as p:
        browser = p.chromium.launch(
            headless=False,  # Zet op True voor headless mode
            args=[
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-blink-features=AutomationControlled',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-background-timer-throttling',
                '--disable-hang-monitor',
                '--disable-prompt-on-repost',
                '--disable-client-side-phishing-detection',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-features=TranslateUI',
                '--disable-web-security',
                '--no-sandbox',
                '--use-gl=desktop',
                '--enable-webgl',
                '--ignore-gpu-blacklist',
                '--enable-gpu-rasterization'
            ]
        )

        context = browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            locale="nl-NL",
            viewport={"width": 1366, "height": 768},
            device_scale_factor=1,
            is_mobile=False,
            has_touch=False,
            color_scheme="light",
            reduced_motion="no-preference",
            forced_colors="none",
            extra_http_headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "nl-NL,nl;q=0.9,en-US;q=0.8,en;q=0.7",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Cache-Control": "max-age=0"
            }
        )

        page = context.new_page()

        # Setup stealth voor deze page
        setup_stealth_page(page)

        try:
            # Extra script na het laden van de pagina voor extra zekerheid
            page.evaluate("""
            // Verwijder webdriver opnieuw na het laden van de pagina
            try {
              const navigatorProto = Object.getPrototypeOf(navigator);
              if (navigatorProto && 'webdriver' in navigatorProto) {
                delete navigatorProto.webdriver;
              }
              if ('webdriver' in navigator) {
                delete navigator.webdriver;
              }
              if ('webdriver' in window) {
                delete window.webdriver;
              }
            } catch(e) {}
            """)

            # Login
            if not login_werkzoeken(page):
                print("❌ Login gefaald. Script wordt gestopt.")
                return

            # Zoek kandidaten
            zoek_kandidaten(page, context)

            # Uitloggen
            print("🔓 Uitloggen...")
            wacht_random()
            page.goto("https://www.werkzoeken.nl/uitloggen")

            print("✅ Script succesvol voltooid!")

        except Exception as e:
            print(f"❌ Er is een fout opgetreden: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Cleanup
            try:
                context.close()
                browser.close()
            except:
                pass  # Browser is al gesloten

if __name__ == "__main__":
    main()
