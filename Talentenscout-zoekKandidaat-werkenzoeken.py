from playwright.sync_api import sync_playwright
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from url import genereer_werkzoeken_url, lees_prompt, stuur_prompt_naar_groq
from bs4 import BeautifulSoup


zoek_url = genereer_werkzoeken_url(115)
print("##### output van de url")
print(zoek_url)


import os
from dotenv import load_dotenv
import time
import random
import sys
from random import randint


from pathlib import Path
from dotenv import load_dotenv

dotenv_path = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=dotenv_path)

email = os.getenv("WERKZOEKEN_EMAIL")
wachtwoord = os.getenv("WERKZOEKEN_WACHTWOORD")


if not email or not wachtwoord:
    raise ValueError("❌ Inloggegevens niet gevonden in .env bestand.")

def wacht_random(min_sec=2.0, max_sec=5.0):
    time.sleep(random.uniform(min_sec, max_sec))


stealth_js = """
// 1. navigator.webdriver = false
Object.defineProperty(navigator, 'webdriver', {
  get: () => false,
});

// 2. window.chrome spoofing
window.chrome = {
  runtime: {},
  loadTimes: () => { },
  csi: () => { },
};

// 3. navigator.plugins spoofing
Object.defineProperty(navigator, 'plugins', {
  get: () => [1, 2, 3],
});

// 4. navigator.languages spoofing
Object.defineProperty(navigator, 'languages', {
  get: () => ['nl-NL', 'nl'],
});

// 5. navigator.permissions spoofing
const originalQuery = window.navigator.permissions.query;
window.navigator.permissions.__proto__.query = (parameters) => (
  parameters.name === 'notifications' ?
    Promise.resolve({ state: Notification.permission }) :
    originalQuery(parameters)
);

// 6. WebGL Vendor and Renderer spoofing
const getParameter = WebGLRenderingContext.prototype.getParameter;
WebGLRenderingContext.prototype.getParameter = function(parameter) {
  if (parameter === 37445) return 'Intel Inc.';
  if (parameter === 37446) return 'Intel Iris OpenGL Engine';
  return getParameter.call(this, parameter);
};

// 7. Navigator userAgent spoofing
Object.defineProperty(navigator, 'userAgent', {
  get: () => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36"
});

// 8. IFrame detection spoofing
Object.defineProperty(HTMLIFrameElement.prototype, 'contentWindow', {
  get: function() {
    return window;
  }
});

// 9. screen.avail* spoofing (bijv. screen.availTop)
Object.defineProperty(screen, 'availTop', {
  get: () => 0,
});
Object.defineProperty(screen, 'availLeft', {
  get: () => 0,
});
"""


def menselijk_typen(page, selector, tekst):
    for letter in tekst:
        page.type(selector, letter, delay=randint(50, 200))
    wacht_random(0.2, 0.7)


with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()

# Injecteer stealth vóór pagina laadt
    page.add_init_script(stealth_js)

    page.goto("https://www.werkzoeken.nl/inloggen")

    page.wait_for_selector("text=Accepteren")
    wacht_random()
    page.click("text=Accepteren")
    wacht_random()
    # Muis bewegen naar het veld
    page.mouse.move(randint(100, 300), randint(200, 250))
    wacht_random()
    # Inloggen met typen
    menselijk_typen(page, "#username", email)
    menselijk_typen(page, "#signin_modal_password", wachtwoord)
    wacht_random()
    page.mouse.move(randint(200, 400), randint(300, 350))
    page.click("id=login_modal_submit")
    wacht_random()

    # Controleer op foutmelding 'Te veel gebruikers ingelogd op dit account.'
    try:
        page.wait_for_selector("text=Te veel gebruikers ingelogd op dit account.", timeout=3000)
        print("❌ Te veel gebruikers ingelogd op dit account. Script wordt gestopt.")
        wacht_random()
        page.goto("https://www.werkzoeken.nl/uitloggen")
        wacht_random()

        context.close()
        browser.close()
        sys.exit()
    except PlaywrightTimeoutError:
        pass  # Geen foutmelding gevonden, ga verder

    page.goto(zoek_url)
    page.wait_for_selector(".vacancies-wrapper")

    # Selecteer alle CV-links
    cvs = page.query_selector_all("a.vacancy.vac.cv")

    print(f"🔎 Gevonden {len(cvs)} CV's:\n")

    for cv in cvs:
        naam = cv.query_selector("h3").inner_text().strip() if cv.query_selector("h3") else "Onbekend"
        plaats = cv.query_selector("strong").inner_text().strip() if cv.query_selector("strong") else "Onbekend"
        functie = cv.query_selector(".tag").inner_text().strip() if cv.query_selector(".tag") else "Onbekend"
        data_url = cv.get_attribute("data-url") or "Geen URL gevonden"
        
        
        print(f"👤 {naam} – 📍 {plaats} – 🏷️ {functie}")
        print(f"🔗 {data_url}")
        

        if data_url != "Geen URL gevonden":
          detail_page = context.new_page()
          detail_page.goto(data_url)
          try:
              detail_page.wait_for_selector("div.vacancy-detail-wrapper", timeout=5000)
              html = detail_page.query_selector("div.vacancy-detail-wrapper").inner_html()
              # Alleen de tekst extraheren
              soup = BeautifulSoup(html, "html.parser")
              alleen_tekst = soup.get_text(separator="\n", strip=True)
              prompt = lees_prompt("werkzoeken_html_profiel_tekst.txt")
              groq_output = stuur_prompt_naar_groq(prompt, alleen_tekst)
              print("Groq output:")
              print(groq_output)
          except PlaywrightTimeoutError:
              print("❌ Kon vacancy-detail-wrapper niet vinden.")
          detail_page.close()
          print("-" * 40)

       
        
    wacht_random()
    page.goto("https://www.werkzoeken.nl/uitloggen")


    # ---------------------
    context.close()
    browser.close()
